<template>
  <div v-if="isVisible" class="modal-overlay" @click="closeModal">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h3>{{ title }}</h3>
        <button @click="closeModal" class="close-button">✕</button>
      </div>
      <div class="modal-body">
        <component :is="getInteractiveComponent(type)" :data="data" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, defineComponent, h, ref } from 'vue'

interface Props {
  isVisible: boolean
  type: string
  title: string
  data?: any
}

const props = defineProps<Props>()
const emit = defineEmits<{
  close: []
}>()

const closeModal = () => {
  emit('close')
}

// 通用交互组件
const DefaultInteractive = defineComponent({
  name: 'DefaultInteractive',
  props: ['data'],
  setup(props) {
    return () => h('div', { class: 'default-interactive' }, [
      h('h4', '🚧 功能开发中'),
      h('p', '这个交互功能正在开发中，敬请期待！'),
      h('div', { class: 'coming-soon' }, [
        h('div', { class: 'spinner' }),
        h('span', '即将推出...')
      ])
    ])
  }
})

const CodeComparison = defineComponent({
  name: 'CodeComparison',
  props: ['data'],
  setup(props) {
    const examples = ref([
      {
        title: '基础示例',
        before: '// 传统写法\nfor (int i = 0; i < list.size(); i++) {\n    System.out.println(list.get(i));\n}',
        after: '// 现代写法\nlist.forEach(System.out::println);'
      }
    ])
    
    const selectedExample = ref(0)
    
    return () => h('div', { class: 'code-comparison' }, [
      h('div', { class: 'example-tabs' }, 
        examples.value.map((example, index) => 
          h('button', {
            key: index,
            onClick: () => selectedExample.value = index,
            class: ['tab-button', { active: selectedExample.value === index }]
          }, example.title)
        )
      ),
      h('div', { class: 'comparison-content' }, [
        h('div', { class: 'code-section' }, [
          h('h4', '传统方式'),
          h('pre', { class: 'code-block' }, examples.value[selectedExample.value].before)
        ]),
        h('div', { class: 'code-section' }, [
          h('h4', '现代方式'),
          h('pre', { class: 'code-block' }, examples.value[selectedExample.value].after)
        ])
      ])
    ])
  }
})

const ConceptDemo = defineComponent({
  name: 'ConceptDemo',
  props: ['data'],
  setup(props) {
    const step = ref(0)
    const steps = ref([
      { title: '步骤 1', description: '初始化概念', code: 'System.out.println("Hello World");' },
      { title: '步骤 2', description: '应用概念', code: 'List<String> list = Arrays.asList("a", "b", "c");' },
      { title: '步骤 3', description: '验证结果', code: 'list.forEach(System.out::println);' }
    ])
    
    const nextStep = () => {
      if (step.value < steps.value.length - 1) {
        step.value++
      }
    }
    
    const prevStep = () => {
      if (step.value > 0) {
        step.value--
      }
    }
    
    const resetDemo = () => {
      step.value = 0
    }
    
    return () => h('div', { class: 'concept-demo' }, [
      h('div', { class: 'demo-header' }, [
        h('h4', `${steps.value[step.value].title}: ${steps.value[step.value].description}`),
        h('div', { class: 'step-indicator' }, `${step.value + 1} / ${steps.value.length}`)
      ]),
      h('div', { class: 'demo-content' }, [
        h('pre', { class: 'code-block' }, steps.value[step.value].code)
      ]),
      h('div', { class: 'demo-controls' }, [
        h('button', {
          onClick: prevStep,
          disabled: step.value === 0,
          class: 'control-button'
        }, '⬅️ 上一步'),
        h('button', {
          onClick: resetDemo,
          class: 'control-button'
        }, '🔄 重置'),
        h('button', {
          onClick: nextStep,
          disabled: step.value === steps.value.length - 1,
          class: 'control-button'
        }, '下一步 ➡️')
      ])
    ])
  }
})

const InteractivePlayground = defineComponent({
  name: 'InteractivePlayground',
  props: ['data'],
  setup(props) {
    const code = ref('// 在这里编写代码\nSystem.out.println("Hello, Interactive World!");')
    const output = ref('')
    const isRunning = ref(false)
    
    const runCode = async () => {
      isRunning.value = true
      output.value = '正在执行...\n'
      
      // 模拟代码执行
      await new Promise(resolve => setTimeout(resolve, 1000))
      output.value = 'Hello, Interactive World!\n执行完成！'
      
      isRunning.value = false
    }
    
    return () => h('div', { class: 'interactive-playground' }, [
      h('h4', '💻 交互式代码编辑器'),
      h('textarea', {
        value: code.value,
        onInput: (e: any) => code.value = e.target.value,
        class: 'code-editor',
        rows: 8,
        placeholder: '在这里编写你的代码...'
      }),
      h('button', {
        onClick: runCode,
        disabled: isRunning.value,
        class: 'run-button'
      }, isRunning.value ? '⏳ 运行中...' : '▶️ 运行代码'),
      h('div', { class: 'result-section' }, [
        h('h4', '📤 输出结果'),
        h('pre', { class: 'result-output' }, output.value)
      ])
    ])
  }
})

const getInteractiveComponent = (type: string) => {
  const componentMap: Record<string, any> = {
    // 通用组件
    'code-comparison': CodeComparison,
    'concept-demo': ConceptDemo,
    'interactive-playground': InteractivePlayground,
    
    // 第6章 - 并发编程
    'lock-demo': ConceptDemo,
    'atomic-demo': InteractivePlayground,
    'blocking-queue-demo': ConceptDemo,
    'future-demo': InteractivePlayground,
    'executor-demo': ConceptDemo,
    
    // 第7章 - 性能优化
    'memory-hierarchy-demo': ConceptDemo,
    'gc-process-demo': InteractivePlayground,
    'jit-demo': ConceptDemo,
    'jfr-recording-demo': InteractivePlayground,
    
    // 第8章 - JVM语言
    'language-comparison': CodeComparison,
    'pyramid-demo': ConceptDemo,
    'selection-checklist': InteractivePlayground,
    'bytecode-demo': CodeComparison,
    'risk-assessment': ConceptDemo,
    'interop-demo': CodeComparison,
    
    // 第9章 - Kotlin (已在 JavaChapter9.vue 中定义)
    'java-kotlin-converter': 'JavaKotlinConverter',
    'syntax-comparison': 'SyntaxComparison',
    'data-class-generator': 'DataClassGenerator',
    'null-safety-playground': 'NullSafetyPlayground',
    'coroutines-playground': 'CoroutinesPlayground',
  }
  
  return componentMap[type] || DefaultInteractive
}
</script>

<style scoped>
/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  max-width: 90vw;
  max-height: 90vh;
  width: 800px;
  overflow: hidden;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.3rem;
}

.close-button {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: background 0.2s ease;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.2);
}

.modal-body {
  padding: 2rem;
  max-height: 70vh;
  overflow-y: auto;
}

/* 交互组件通用样式 */
.default-interactive {
  text-align: center;
  padding: 2rem;
}

.coming-soon {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;
  color: #666;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #e9ecef;
  border-top: 2px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.code-comparison {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.example-tabs {
  display: flex;
  gap: 0.5rem;
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 1rem;
}

.tab-button {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tab-button.active {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.comparison-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.code-section {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.code-section h4 {
  margin: 0;
  color: #333;
  font-size: 1rem;
}

.code-block {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
  line-height: 1.4;
  margin: 0;
  white-space: pre-wrap;
}

.concept-demo {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.demo-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 6px;
}

.demo-header h4 {
  margin: 0;
  color: #333;
}

.step-indicator {
  background: #667eea;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
}

.demo-controls {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.control-button {
  background: #667eea;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.control-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.control-button:hover:not(:disabled) {
  background: #5a6fd8;
}

.interactive-playground {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.code-editor {
  width: 100%;
  padding: 1rem;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  line-height: 1.4;
  resize: vertical;
}

.run-button {
  background: #4caf50;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  align-self: flex-start;
}

.run-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.result-section h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
}

.result-output {
  background: #2d3748;
  color: #e2e8f0;
  padding: 1rem;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  margin: 0;
  min-height: 100px;
  white-space: pre-wrap;
}
</style>

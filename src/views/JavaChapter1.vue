<template>
  <div class="java-chapter1">
    <!-- 页面头部 -->
    <div class="chapter-header">
      <div class="container">
        <div class="header-content">
          <h1 class="chapter-title">第一章：Java 语言与平台基础</h1>
          <p class="chapter-subtitle">深入理解 Java 语言特性与平台演进</p>
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: progress + '%' }"></div>
            <span class="progress-text">{{ progress }}% 完成</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="container">
      <div class="content-wrapper">
        <div class="content-layout">
          <!-- 侧边栏导航 -->
          <aside class="sidebar">
            <div class="outline">
              <h3>📚 章节大纲</h3>
              <div class="outline-grid">
                <div
                  v-for="(topic, index) in courseTopics"
                  :key="index"
                  @click="scrollToTopic(index)"
                  :class="['outline-item', { active: currentTopic === index }]"
                >
                  <div class="outline-number">{{ index + 1 }}</div>
                  <div class="outline-content">
                    <h4>{{ topic.title }}</h4>
                    <p>{{ topic.description }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 工具栏 -->
            <div class="toolbar">
              <button @click="toggleNotes" class="tool-button">📝 笔记</button>
              <button @click="showQuiz" class="tool-button">🧠 测验</button>
            </div>
          </aside>

          <!-- 主内容区 -->
          <main class="main-content">
            <!-- Topic 1: Java语言 vs Java平台 -->
            <section id="topic-0" class="topic-section" ref="topic0">
              <ExpandableSection
                title="Java 语言 vs. Java 平台"
                :concept-data="javaLanguagePlatformData"
                @interaction="handleInteraction"
              >
                <JavaCompilationAnimation />
                <div class="deep-dive">
                  <h3>🔍 深度解读</h3>
                  <div class="explanation-tabs">
                    <div class="tab-buttons">
                      <button
                        v-for="(tab, index) in explanationTabs"
                        :key="index"
                        @click="activeTab = index"
                        :class="['tab-button', { active: activeTab === index }]"
                      >
                        {{ tab.title }}
                      </button>
                    </div>
                    <div class="tab-content">
                      <div
                        v-for="(tab, index) in explanationTabs"
                        :key="index"
                        v-show="activeTab === index"
                        class="tab-panel"
                      >
                        <h4>{{ tab.title }}</h4>
                        <div v-if="tab.type === 'definitions'" class="definition-cards">
                          <div
                            v-for="def in tab.content"
                            :key="def.title"
                            :class="['definition-card', def.type]"
                          >
                            <h5>{{ def.title }}</h5>
                            <p>{{ def.description }}</p>
                            <ul>
                              <li v-for="feature in def.features" :key="feature">{{ feature }}</li>
                            </ul>
                          </div>
                        </div>
                        <div v-else-if="tab.type === 'timeline'" class="timeline">
                          <div
                            v-for="item in tab.content"
                            :key="item.version"
                            :class="['timeline-item', { lts: item.isLTS }]"
                          >
                            <div class="timeline-marker"></div>
                            <div class="timeline-content">
                              <h5>
                                {{ item.version }}
                                <span v-if="item.isLTS" class="lts-badge">LTS</span>
                              </h5>
                              <p class="timeline-date">{{ item.date }}</p>
                              <p>{{ item.description }}</p>
                              <ul v-if="item.features">
                                <li v-for="feature in item.features" :key="feature">
                                  {{ feature }}
                                </li>
                              </ul>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- F. 项目实践踩坑与解决方案 -->
                <div class="real-world-problems">
                  <h3>🔧 项目实践踩坑与解决方案</h3>

                  <div class="problem-section">
                    <h4>💥 常见问题描述</h4>
                    <div class="problem-cards">
                      <div class="problem-card critical">
                        <div class="problem-header">
                          <span class="problem-icon">⚠️</span>
                          <h5>跨平台部署时的JVM版本不一致问题</h5>
                        </div>
                        <div class="problem-content">
                          <p>
                            <strong>场景：</strong>在微服务架构中，开发环境使用Java
                            17，但生产环境的某些节点仍运行Java 8，导致应用无法启动或出现运行时异常。
                          </p>
                          <p>
                            <strong>表现：</strong
                            >ClassNotFoundException、UnsupportedClassVersionError、或某些新API调用失败。
                          </p>
                        </div>
                      </div>

                      <div class="problem-card warning">
                        <div class="problem-header">
                          <span class="problem-icon">🐛</span>
                          <h5>字节码兼容性导致的性能退化</h5>
                        </div>
                        <div class="problem-content">
                          <p>
                            <strong>场景：</strong
                            >使用高版本JDK编译的代码在低版本JVM上运行时，某些优化失效，导致性能比预期下降30-50%。
                          </p>
                          <p>
                            <strong>表现：</strong
                            >相同业务逻辑在不同环境下性能差异巨大，特别是涉及集合操作和字符串处理的场景。
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="root-cause-analysis">
                    <h4>🔍 问题根源分析</h4>
                    <div class="analysis-content">
                      <div class="cause-item">
                        <span class="cause-icon">🎯</span>
                        <div>
                          <h5>技术本身的设计权衡</h5>
                          <p>
                            Java的"一次编写，到处运行"承诺在现实中受到版本兼容性的制约。字节码格式的向前兼容性设计导致新特性无法在旧JVM上使用。
                          </p>
                        </div>
                      </div>
                      <div class="cause-item">
                        <span class="cause-icon">🏗️</span>
                        <div>
                          <h5>企业环境的复杂性</h5>
                          <p>
                            大型企业往往有多套环境（开发、测试、预发、生产），每套环境的JVM版本可能不同，加上遗留系统的约束，形成了复杂的版本矩阵。
                          </p>
                        </div>
                      </div>
                      <div class="cause-item">
                        <span class="cause-icon">⚙️</span>
                        <div>
                          <h5>工程实践的疏忽</h5>
                          <p>
                            缺乏统一的环境管理策略，没有在CI/CD流程中强制进行跨版本兼容性测试，导致问题在生产环境才暴露。
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="solutions-section">
                    <h4>💡 业界主流解决方案与权衡</h4>
                    <div class="solutions-grid">
                      <div class="solution-card recommended">
                        <div class="solution-header">
                          <span class="solution-icon">🐳</span>
                          <h5>容器化统一运行时环境</h5>
                          <span class="solution-badge best">推荐方案</span>
                        </div>
                        <div class="solution-content">
                          <p>
                            <strong>方案：</strong
                            >使用Docker等容器技术，将应用和特定版本的JVM打包在一起，确保所有环境使用相同的运行时。
                          </p>
                          <div class="pros-cons">
                            <div class="pros">
                              <h6>✅ 优势</h6>
                              <ul>
                                <li>彻底解决版本不一致问题</li>
                                <li>环境隔离，减少依赖冲突</li>
                                <li>便于版本管理和回滚</li>
                                <li>支持蓝绿部署等高级策略</li>
                              </ul>
                            </div>
                            <div class="cons">
                              <h6>❌ 劣势</h6>
                              <ul>
                                <li>增加了基础设施复杂度</li>
                                <li>容器镜像体积较大（200MB+）</li>
                                <li>需要团队掌握容器技术</li>
                                <li>可能增加内存开销</li>
                              </ul>
                            </div>
                          </div>
                          <div class="trade-offs">
                            <p>
                              <strong>权衡：</strong
                              >适合中大型项目，初期投入较高但长期收益显著。是目前云原生架构的标准做法。
                            </p>
                          </div>
                        </div>
                      </div>

                      <div class="solution-card alternative">
                        <div class="solution-header">
                          <span class="solution-icon">📋</span>
                          <h5>多版本兼容性测试矩阵</h5>
                          <span class="solution-badge stable">稳妥方案</span>
                        </div>
                        <div class="solution-content">
                          <p>
                            <strong>方案：</strong
                            >在CI/CD流程中建立多JVM版本的自动化测试矩阵，确保代码在目标环境的所有JVM版本上都能正常运行。
                          </p>
                          <div class="pros-cons">
                            <div class="pros">
                              <h6>✅ 优势</h6>
                              <ul>
                                <li>无需改变现有部署架构</li>
                                <li>早期发现兼容性问题</li>
                                <li>成本相对较低</li>
                                <li>团队学习成本小</li>
                              </ul>
                            </div>
                            <div class="cons">
                              <h6>❌ 劣势</h6>
                              <ul>
                                <li>测试时间成倍增加</li>
                                <li>仍需维护多套JVM环境</li>
                                <li>无法解决性能差异问题</li>
                                <li>限制了新特性的使用</li>
                              </ul>
                            </div>
                          </div>
                          <div class="trade-offs">
                            <p>
                              <strong>权衡：</strong
                              >适合保守型企业或遗留系统较多的场景，能够在现有架构基础上提升稳定性。
                            </p>
                          </div>
                        </div>
                      </div>

                      <div class="solution-card temporary">
                        <div class="solution-header">
                          <span class="solution-icon">🔧</span>
                          <h5>JVM参数调优与降级策略</h5>
                          <span class="solution-badge quick">快速方案</span>
                        </div>
                        <div class="solution-content">
                          <p>
                            <strong>方案：</strong
                            >通过JVM启动参数调优，配合代码中的版本检测和功能降级，在不同JVM版本间提供基本兼容性。
                          </p>
                          <div class="pros-cons">
                            <div class="pros">
                              <h6>✅ 优势</h6>
                              <ul>
                                <li>实施速度快，立即见效</li>
                                <li>不需要架构调整</li>
                                <li>可以作为临时解决方案</li>
                                <li>对现有代码影响最小</li>
                              </ul>
                            </div>
                            <div class="cons">
                              <h6>❌ 劣势</h6>
                              <ul>
                                <li>治标不治本，技术债务积累</li>
                                <li>代码复杂度增加</li>
                                <li>性能可能无法完全优化</li>
                                <li>维护成本持续增长</li>
                              </ul>
                            </div>
                          </div>
                          <div class="trade-offs">
                            <p>
                              <strong>权衡：</strong
                              >适合作为紧急修复手段或向更好方案迁移的过渡期解决方案，不建议长期使用。
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 2: 新的Java发布模型 -->
            <section id="topic-1" class="topic-section" ref="topic1">
              <ExpandableSection
                title="新的 Java 发布模型"
                :concept-data="releaseModelData"
                @interaction="handleInteraction"
              >
                <div class="release-timeline">
                  <h3>📅 Java 发布时间线</h3>
                  <div class="timeline-container">
                    <div
                      v-for="release in javaReleases"
                      :key="release.version"
                      :class="['timeline-item', { lts: release.isLTS }]"
                    >
                      <div class="timeline-marker"></div>
                      <div class="timeline-content">
                        <h4>
                          Java {{ release.version }}
                          <span v-if="release.isLTS" class="lts-badge">LTS</span>
                        </h4>
                        <p class="timeline-date">{{ release.date }}</p>
                        <p class="timeline-features">{{ release.features }}</p>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- F. 项目实践踩坑与解决方案 -->
                <div class="real-world-problems">
                  <h3>🔧 项目实践踩坑与解决方案</h3>

                  <div class="problem-section">
                    <h4>💥 常见问题描述</h4>
                    <div class="problem-cards">
                      <div class="problem-card critical">
                        <div class="problem-header">
                          <span class="problem-icon">📅</span>
                          <h5>LTS版本选择困境与升级时机把握</h5>
                        </div>
                        <div class="problem-content">
                          <p>
                            <strong>场景：</strong>企业级项目面临Java 8
                            LTS即将停止免费支持，需要升级到Java
                            11或17，但担心新版本的稳定性和兼容性问题。
                          </p>
                          <p>
                            <strong>表现：</strong>团队在Java
                            11、17、21之间犹豫不决，错过最佳升级窗口期，最终被迫在压力下匆忙升级，导致生产事故。
                          </p>
                        </div>
                      </div>

                      <div class="problem-card warning">
                        <div class="problem-header">
                          <span class="problem-icon">🔄</span>
                          <h5>频繁版本发布带来的依赖管理混乱</h5>
                        </div>
                        <div class="problem-content">
                          <p>
                            <strong>场景：</strong
                            >第三方库跟进Java新版本的速度不一致，导致项目依赖树中出现版本冲突，特别是Spring、Hibernate等核心框架的兼容性问题。
                          </p>
                          <p>
                            <strong>表现：</strong
                            >Maven/Gradle构建失败，运行时出现NoSuchMethodError，或者被迫使用过时的库版本无法享受新特性。
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="root-cause-analysis">
                    <h4>🔍 问题根源分析</h4>
                    <div class="analysis-content">
                      <div class="cause-item">
                        <span class="cause-icon">⏰</span>
                        <div>
                          <h5>发布节奏与企业决策周期不匹配</h5>
                          <p>
                            Java
                            6个月的发布周期远快于企业的技术决策和升级周期（通常1-2年），导致企业总是处于"追赶"状态而非"规划"状态。
                          </p>
                        </div>
                      </div>
                      <div class="cause-item">
                        <span class="cause-icon">🌐</span>
                        <div>
                          <h5>生态系统成熟度的时间差</h5>
                          <p>
                            Java新版本发布后，整个生态系统（IDE、框架、工具链、云服务）需要6-12个月才能完全跟进，形成了"可用"与"生产就绪"之间的时间窗口。
                          </p>
                        </div>
                      </div>
                      <div class="cause-item">
                        <span class="cause-icon">💼</span>
                        <div>
                          <h5>企业风险控制与创新的平衡</h5>
                          <p>
                            企业需要在技术创新和系统稳定性之间找平衡，往往倾向于保守策略，但这可能导致技术债务积累和人才流失。
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="solutions-section">
                    <h4>💡 业界主流解决方案与权衡</h4>
                    <div class="solutions-grid">
                      <div class="solution-card recommended">
                        <div class="solution-header">
                          <span class="solution-icon">🎯</span>
                          <h5>"LTS+1"策略</h5>
                          <span class="solution-badge best">推荐方案</span>
                        </div>
                        <div class="solution-content">
                          <p>
                            <strong>方案：</strong
                            >采用"当前LTS版本+下一个LTS版本预研"的双轨策略，生产环境使用稳定的LTS版本，同时在非关键项目中试验下一个LTS版本。
                          </p>
                          <div class="pros-cons">
                            <div class="pros">
                              <h6>✅ 优势</h6>
                              <ul>
                                <li>生产环境稳定性有保障</li>
                                <li>团队有充分时间学习新特性</li>
                                <li>可以提前发现兼容性问题</li>
                                <li>升级时机可控，风险可控</li>
                              </ul>
                            </div>
                            <div class="cons">
                              <h6>❌ 劣势</h6>
                              <ul>
                                <li>需要维护两套开发环境</li>
                                <li>团队学习成本增加</li>
                                <li>可能错过某些中间版本的优秀特性</li>
                                <li>预研项目需要额外资源投入</li>
                              </ul>
                            </div>
                          </div>
                          <div class="trade-offs">
                            <p>
                              <strong>权衡：</strong
                              >适合大中型企业，能够在稳定性和创新性之间取得最佳平衡，是目前最主流的企业级Java版本管理策略。
                            </p>
                          </div>
                        </div>
                      </div>

                      <div class="solution-card alternative">
                        <div class="solution-header">
                          <span class="solution-icon">🔄</span>
                          <h5>渐进式升级与特性开关</h5>
                          <span class="solution-badge stable">稳妥方案</span>
                        </div>
                        <div class="solution-content">
                          <p>
                            <strong>方案：</strong
                            >建立特性开关机制，在同一代码库中支持多个Java版本，通过配置控制新特性的启用，实现渐进式升级。
                          </p>
                          <div class="pros-cons">
                            <div class="pros">
                              <h6>✅ 优势</h6>
                              <ul>
                                <li>升级风险可控，可随时回滚</li>
                                <li>可以按模块逐步升级</li>
                                <li>便于A/B测试和灰度发布</li>
                                <li>团队可以逐步适应新特性</li>
                              </ul>
                            </div>
                            <div class="cons">
                              <h6>❌ 劣势</h6>
                              <ul>
                                <li>代码复杂度显著增加</li>
                                <li>需要大量的条件编译逻辑</li>
                                <li>测试矩阵呈指数级增长</li>
                                <li>长期维护成本很高</li>
                              </ul>
                            </div>
                          </div>
                          <div class="trade-offs">
                            <p>
                              <strong>权衡：</strong
                              >适合对稳定性要求极高的金融、医疗等行业，但需要强大的工程能力支撑，不建议小团队采用。
                            </p>
                          </div>
                        </div>
                      </div>

                      <div class="solution-card temporary">
                        <div class="solution-header">
                          <span class="solution-icon">⚡</span>
                          <h5>激进跟进策略</h5>
                          <span class="solution-badge risky">高风险方案</span>
                        </div>
                        <div class="solution-content">
                          <p>
                            <strong>方案：</strong
                            >紧跟Java最新版本，在新版本发布后3-6个月内完成升级，优先享受新特性和性能改进。
                          </p>
                          <div class="pros-cons">
                            <div class="pros">
                              <h6>✅ 优势</h6>
                              <ul>
                                <li>始终使用最新特性和性能优化</li>
                                <li>团队技术能力提升快</li>
                                <li>有利于招聘和人才保留</li>
                                <li>避免技术债务积累</li>
                              </ul>
                            </div>
                            <div class="cons">
                              <h6>❌ 劣势</h6>
                              <ul>
                                <li>生产环境风险极高</li>
                                <li>生态系统兼容性问题频发</li>
                                <li>升级成本和频率都很高</li>
                                <li>可能遇到新版本的bug</li>
                              </ul>
                            </div>
                          </div>
                          <div class="trade-offs">
                            <p>
                              <strong>权衡：</strong
                              >仅适合技术驱动的创业公司或内部工具项目，对于业务关键系统风险过高，需要非常强的技术团队支撑。
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 3: var关键字 -->
            <section id="topic-2" class="topic-section" ref="topic2">
              <ExpandableSection
                title="增强的类型推断 (var 关键字)"
                :concept-data="varKeywordData"
                @interaction="handleInteraction"
              >
                <CodePlayground
                  :examples="varExamples"
                  title="var 关键字实践"
                  @code-run="handleCodeRun"
                />

                <!-- F. 项目实践踩坑与解决方案 -->
                <div class="real-world-problems">
                  <h3>🔧 项目实践踩坑与解决方案</h3>

                  <div class="problem-section">
                    <h4>💥 常见问题描述</h4>
                    <div class="problem-cards">
                      <div class="problem-card critical">
                        <div class="problem-header">
                          <span class="problem-icon">🔍</span>
                          <h5>var滥用导致代码可读性急剧下降</h5>
                        </div>
                        <div class="problem-content">
                          <p>
                            <strong>场景：</strong
                            >团队成员过度使用var关键字，特别是在复杂的泛型、Lambda表达式和方法链调用中，导致代码审查时无法快速理解变量类型。
                          </p>
                          <p>
                            <strong>表现：</strong>代码如<code
                              >var result = service.process().filter(...).map(...)</code
                            >，审查者需要追溯整个调用链才能确定result的实际类型，严重影响开发效率。
                          </p>
                        </div>
                      </div>

                      <div class="problem-card warning">
                        <div class="problem-header">
                          <span class="problem-icon">🐛</span>
                          <h5>IDE自动补全失效与重构困难</h5>
                        </div>
                        <div class="problem-content">
                          <p>
                            <strong>场景：</strong
                            >在大型项目中，过度使用var导致IDE的类型推断变慢，自动补全功能失效，重构时无法准确识别类型依赖关系。
                          </p>
                          <p>
                            <strong>表现：</strong
                            >IDE响应缓慢，重构操作可能遗漏某些使用点，单元测试中的mock对象创建变得困难。
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="root-cause-analysis">
                    <h4>🔍 问题根源分析</h4>
                    <div class="analysis-content">
                      <div class="cause-item">
                        <span class="cause-icon">🎯</span>
                        <div>
                          <h5>语法糖的双刃剑特性</h5>
                          <p>
                            var关键字设计初衷是减少冗余代码，但过度使用会牺牲类型信息的明确性。Java作为静态类型语言，类型信息是代码自文档化的重要组成部分。
                          </p>
                        </div>
                      </div>
                      <div class="cause-item">
                        <span class="cause-icon">👥</span>
                        <div>
                          <h5>团队编码规范缺失</h5>
                          <p>
                            很多团队引入var后没有建立明确的使用规范，导致不同开发者有不同的使用习惯，代码风格不一致，增加了维护成本。
                          </p>
                        </div>
                      </div>
                      <div class="cause-item">
                        <span class="cause-icon">⚙️</span>
                        <div>
                          <h5>工具链适配的滞后性</h5>
                          <p>
                            某些静态分析工具、代码生成工具对var的支持不够完善，在复杂的企业级项目中可能出现兼容性问题。
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="solutions-section">
                    <h4>💡 业界主流解决方案与权衡</h4>
                    <div class="solutions-grid">
                      <div class="solution-card recommended">
                        <div class="solution-header">
                          <span class="solution-icon">📋</span>
                          <h5>制定明确的var使用规范</h5>
                          <span class="solution-badge best">推荐方案</span>
                        </div>
                        <div class="solution-content">
                          <p>
                            <strong>方案：</strong
                            >建立团队级别的var使用指南，明确适用场景（如局部变量初始化、复杂泛型简化）和禁用场景（如返回值不明确的方法调用）。
                          </p>
                          <div class="pros-cons">
                            <div class="pros">
                              <h6>✅ 优势</h6>
                              <ul>
                                <li>保持代码风格一致性</li>
                                <li>平衡简洁性和可读性</li>
                                <li>便于代码审查和维护</li>
                                <li>新团队成员容易上手</li>
                              </ul>
                            </div>
                            <div class="cons">
                              <h6>❌ 劣势</h6>
                              <ul>
                                <li>需要团队达成共识</li>
                                <li>规范制定需要时间</li>
                                <li>需要定期审查和更新</li>
                                <li>可能限制某些灵活用法</li>
                              </ul>
                            </div>
                          </div>
                          <div class="trade-offs">
                            <p>
                              <strong>权衡：</strong
                              >适合所有团队，是使用var的最佳实践。短期投入换取长期的代码质量保障，ROI很高。
                            </p>
                          </div>
                        </div>
                      </div>

                      <div class="solution-card alternative">
                        <div class="solution-header">
                          <span class="solution-icon">🔧</span>
                          <h5>静态分析工具强制检查</h5>
                          <span class="solution-badge stable">技术方案</span>
                        </div>
                        <div class="solution-content">
                          <p>
                            <strong>方案：</strong
                            >配置Checkstyle、SpotBugs等静态分析工具，自动检查var的使用是否符合团队规范，在CI/CD流程中强制执行。
                          </p>
                          <div class="pros-cons">
                            <div class="pros">
                              <h6>✅ 优势</h6>
                              <ul>
                                <li>自动化检查，减少人工审查</li>
                                <li>可以集成到开发工具链</li>
                                <li>规则可配置和定制</li>
                                <li>提供即时反馈</li>
                              </ul>
                            </div>
                            <div class="cons">
                              <h6>❌ 劣势</h6>
                              <ul>
                                <li>工具配置复杂</li>
                                <li>可能产生误报</li>
                                <li>无法覆盖所有语义场景</li>
                                <li>增加构建时间</li>
                              </ul>
                            </div>
                          </div>
                          <div class="trade-offs">
                            <p>
                              <strong>权衡：</strong
                              >适合有完善DevOps流程的团队，能够有效防止规范违反，但需要投入时间进行工具配置和维护。
                            </p>
                          </div>
                        </div>
                      </div>

                      <div class="solution-card temporary">
                        <div class="solution-header">
                          <span class="solution-icon">🚫</span>
                          <h5>完全禁用var关键字</h5>
                          <span class="solution-badge conservative">保守方案</span>
                        </div>
                        <div class="solution-content">
                          <p>
                            <strong>方案：</strong
                            >在团队中完全禁用var关键字，继续使用传统的显式类型声明，避免所有相关问题。
                          </p>
                          <div class="pros-cons">
                            <div class="pros">
                              <h6>✅ 优势</h6>
                              <ul>
                                <li>类型信息完全明确</li>
                                <li>避免所有var相关问题</li>
                                <li>工具兼容性最好</li>
                                <li>学习成本为零</li>
                              </ul>
                            </div>
                            <div class="cons">
                              <h6>❌ 劣势</h6>
                              <ul>
                                <li>错过语言改进带来的便利</li>
                                <li>复杂泛型代码冗长</li>
                                <li>可能影响团队技术形象</li>
                                <li>与现代Java实践脱节</li>
                              </ul>
                            </div>
                          </div>
                          <div class="trade-offs">
                            <p>
                              <strong>权衡：</strong
                              >适合保守型企业或遗留系统维护团队，但会错过Java现代化特性，不利于长期技术发展。
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 4: 语言与平台演进机制 -->
            <section id="topic-3" class="topic-section" ref="topic3">
              <ExpandableSection
                title="语言与平台的演进机制"
                :concept-data="evolutionMechanismData"
                @interaction="handleInteraction"
              >
                <div class="evolution-mechanisms">
                  <h3>🔧 演进机制详解</h3>
                  <div class="mechanisms-grid">
                    <div class="mechanism-card syntactic-sugar">
                      <div class="mechanism-header">
                        <span class="mechanism-icon">🍬</span>
                        <h4>语法糖 (Syntactic Sugar)</h4>
                        <span class="cost-badge low">成本最低</span>
                      </div>
                      <div class="mechanism-content">
                        <p><strong>定义：</strong>编译器层面的语法简化，不涉及JVM变更</p>
                        <p><strong>示例：</strong>try-with-resources, enhanced for loop</p>
                        <p><strong>特点：</strong>编译时"脱糖"，转换为基础语法</p>
                      </div>
                    </div>

                    <div class="mechanism-card jeps-jsrs">
                      <div class="mechanism-header">
                        <span class="mechanism-icon">📋</span>
                        <h4>JEPs & JSRs</h4>
                        <span class="cost-badge medium">成本中等</span>
                      </div>
                      <div class="mechanism-content">
                        <p><strong>JSR：</strong>Java规范请求，流程较重，适合成熟技术</p>
                        <p><strong>JEP：</strong>JDK增强提案，轻量级，快速推动变更</p>
                        <p><strong>关系：</strong>大JSR通常由多个小JEP组成</p>
                      </div>
                    </div>

                    <div class="mechanism-card preview-features">
                      <div class="mechanism-header">
                        <span class="mechanism-icon">🧪</span>
                        <h4>预览特性 (Preview)</h4>
                        <span class="cost-badge high">风险较高</span>
                      </div>
                      <div class="mechanism-content">
                        <p><strong>目的：</strong>收集社区反馈，验证设计</p>
                        <p><strong>使用：</strong>需要--enable-preview标志</p>
                        <p><strong>警告：</strong>绝不可用于生产环境！</p>
                      </div>
                    </div>

                    <div class="mechanism-card incubating-features">
                      <div class="mechanism-header">
                        <span class="mechanism-icon">🥚</span>
                        <h4>孵化特性 (Incubating)</h4>
                        <span class="cost-badge medium">成本中等</span>
                      </div>
                      <div class="mechanism-content">
                        <p><strong>范围：</strong>主要是新的API模块</p>
                        <p><strong>位置：</strong>jdk.incubator包中</p>
                        <p><strong>示例：</strong>HTTP/2客户端最初就是孵化特性</p>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- F. 项目实践踩坑与解决方案 -->
                <div class="real-world-problems">
                  <h3>🔧 项目实践踩坑与解决方案</h3>

                  <div class="problem-section">
                    <h4>💥 常见问题描述</h4>
                    <div class="problem-cards">
                      <div class="problem-card critical">
                        <div class="problem-header">
                          <span class="problem-icon">🧪</span>
                          <h5>预览特性误用导致生产环境故障</h5>
                        </div>
                        <div class="problem-content">
                          <p>
                            <strong>场景：</strong
                            >开发团队在生产环境中使用了Java预览特性（如早期的Pattern
                            Matching、Records等），导致系统在JVM升级后出现兼容性问题或功能异常。
                          </p>
                          <p>
                            <strong>表现：</strong>应用启动失败，出现"Preview feature not
                            enabled"错误，或者预览特性在新版本中语法发生变化导致编译失败。
                          </p>
                        </div>
                      </div>

                      <div class="problem-card warning">
                        <div class="problem-header">
                          <span class="problem-icon">📦</span>
                          <h5>孵化模块依赖导致的部署复杂性</h5>
                        </div>
                        <div class="problem-content">
                          <p>
                            <strong>场景：</strong>项目依赖了jdk.incubator包中的API（如早期的HTTP
                            Client），在模块化部署或容器化环境中出现模块路径配置复杂、依赖解析失败等问题。
                          </p>
                          <p>
                            <strong>表现：</strong>Docker镜像构建失败，模块系统报告"module not
                            found"，或者在不同JDK发行版之间出现兼容性差异。
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="root-cause-analysis">
                    <h4>🔍 问题根源分析</h4>
                    <div class="analysis-content">
                      <div class="cause-item">
                        <span class="cause-icon">🔬</span>
                        <div>
                          <h5>特性成熟度的认知偏差</h5>
                          <p>
                            开发者往往低估了预览特性和孵化特性的实验性质，将其视为"准正式"特性使用，忽略了这些特性可能在后续版本中发生重大变化甚至被移除的风险。
                          </p>
                        </div>
                      </div>
                      <div class="cause-item">
                        <span class="cause-icon">📚</span>
                        <div>
                          <h5>文档和教育的不足</h5>
                          <p>
                            Oracle和社区的文档虽然标明了特性状态，但在实际项目中，这些警告往往被忽视。团队缺乏对Java演进机制的深入理解，没有建立相应的风险评估流程。
                          </p>
                        </div>
                      </div>
                      <div class="cause-item">
                        <span class="cause-icon">⚡</span>
                        <div>
                          <h5>快速迭代与稳定性的矛盾</h5>
                          <p>
                            现代软件开发追求快速迭代，开发者希望尽早使用新特性获得竞争优势，但这与企业级应用对稳定性的要求形成了根本矛盾。
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="solutions-section">
                    <h4>💡 业界主流解决方案与权衡</h4>
                    <div class="solutions-grid">
                      <div class="solution-card recommended">
                        <div class="solution-header">
                          <span class="solution-icon">🛡️</span>
                          <h5>分层特性采用策略</h5>
                          <span class="solution-badge best">推荐方案</span>
                        </div>
                        <div class="solution-content">
                          <p>
                            <strong>方案：</strong
                            >建立特性采用的分层策略：生产环境只使用正式特性，测试环境可以试验孵化特性，开发环境可以探索预览特性。
                          </p>
                          <div class="pros-cons">
                            <div class="pros">
                              <h6>✅ 优势</h6>
                              <ul>
                                <li>生产环境稳定性有保障</li>
                                <li>团队可以提前学习新特性</li>
                                <li>风险可控，影响范围有限</li>
                                <li>便于制定升级计划</li>
                              </ul>
                            </div>
                            <div class="cons">
                              <h6>❌ 劣势</h6>
                              <ul>
                                <li>需要维护多套环境配置</li>
                                <li>增加了环境管理复杂度</li>
                                <li>可能延缓新特性的采用</li>
                                <li>需要额外的测试资源</li>
                              </ul>
                            </div>
                          </div>
                          <div class="trade-offs">
                            <p>
                              <strong>权衡：</strong
                              >适合大多数企业级项目，能够在创新和稳定性之间取得平衡，是目前业界的最佳实践。
                            </p>
                          </div>
                        </div>
                      </div>

                      <div class="solution-card alternative">
                        <div class="solution-header">
                          <span class="solution-icon">🔍</span>
                          <h5>特性状态监控与自动化检查</h5>
                          <span class="solution-badge stable">技术方案</span>
                        </div>
                        <div class="solution-content">
                          <p>
                            <strong>方案：</strong
                            >开发自动化工具检查代码中使用的Java特性状态，在CI/CD流程中自动识别预览特性和孵化特性的使用，并根据部署环境给出警告或阻止部署。
                          </p>
                          <div class="pros-cons">
                            <div class="pros">
                              <h6>✅ 优势</h6>
                              <ul>
                                <li>自动化检查，减少人为疏忽</li>
                                <li>可以集成到现有工具链</li>
                                <li>提供详细的特性使用报告</li>
                                <li>支持自定义规则配置</li>
                              </ul>
                            </div>
                            <div class="cons">
                              <h6>❌ 劣势</h6>
                              <ul>
                                <li>工具开发和维护成本高</li>
                                <li>需要跟进Java版本更新</li>
                                <li>可能产生误报或漏报</li>
                                <li>对复杂代码模式支持有限</li>
                              </ul>
                            </div>
                          </div>
                          <div class="trade-offs">
                            <p>
                              <strong>权衡：</strong
                              >适合有强大技术团队的大型企业，能够提供额外的安全保障，但需要持续投入维护成本。
                            </p>
                          </div>
                        </div>
                      </div>

                      <div class="solution-card temporary">
                        <div class="solution-header">
                          <span class="solution-icon">🔒</span>
                          <h5>保守的特性采用策略</h5>
                          <span class="solution-badge conservative">保守方案</span>
                        </div>
                        <div class="solution-content">
                          <p>
                            <strong>方案：</strong
                            >完全避免使用预览特性和孵化特性，只在特性正式发布并经过至少一个版本周期验证后才考虑采用。
                          </p>
                          <div class="pros-cons">
                            <div class="pros">
                              <h6>✅ 优势</h6>
                              <ul>
                                <li>最大程度保证稳定性</li>
                                <li>避免所有实验性特性风险</li>
                                <li>简化环境管理</li>
                                <li>减少学习和培训成本</li>
                              </ul>
                            </div>
                            <div class="cons">
                              <h6>❌ 劣势</h6>
                              <ul>
                                <li>错过早期采用的竞争优势</li>
                                <li>团队技术能力提升缓慢</li>
                                <li>可能影响人才招聘和保留</li>
                                <li>与技术发展趋势脱节</li>
                              </ul>
                            </div>
                          </div>
                          <div class="trade-offs">
                            <p>
                              <strong>权衡：</strong
                              >适合对稳定性要求极高的关键业务系统，但可能在技术创新方面落后于竞争对手。
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 5: Java 11 重要变更 -->
            <section id="topic-4" class="topic-section" ref="topic4">
              <ExpandableSection
                title="Java 11 的重要小型变更"
                :concept-data="java11ChangesData"
                @interaction="handleInteraction"
              >
                <div class="java11-features">
                  <h3>🚀 Java 11 核心新特性</h3>

                  <!-- 集合工厂 -->
                  <div class="feature-detail">
                    <div class="feature-header">
                      <span class="feature-icon">📦</span>
                      <h4>集合工厂 (JEP 213)</h4>
                      <span class="jep-badge">JEP 213</span>
                    </div>
                    <div class="feature-content">
                      <div class="feature-description">
                        <p>通过静态工厂方法创建不可变集合，大大简化了小型集合的创建过程。</p>
                      </div>
                      <CodePlayground
                        :examples="collectionFactoryExamples"
                        title="集合工厂实践"
                        @code-run="handleCodeRun"
                      />
                    </div>
                  </div>
                </div>

                <!-- F. 项目实践踩坑与解决方案 -->
                <div class="real-world-problems">
                  <h3>🔧 项目实践踩坑与解决方案</h3>

                  <div class="problem-section">
                    <h4>💥 常见问题描述</h4>
                    <div class="problem-cards">
                      <div class="problem-card critical">
                        <div class="problem-header">
                          <span class="problem-icon">📦</span>
                          <h5>集合工厂创建的不可变集合误用</h5>
                        </div>
                        <div class="problem-content">
                          <p>
                            <strong>场景：</strong
                            >开发者使用List.of()、Set.of()等集合工厂方法创建集合后，在后续代码中尝试修改这些集合，导致运行时抛出UnsupportedOperationException异常。
                          </p>
                          <p>
                            <strong>表现：</strong
                            >代码编译正常，但在运行时调用add()、remove()等修改方法时程序崩溃，特别是在单元测试和集成测试中频繁出现。
                          </p>
                        </div>
                      </div>

                      <div class="problem-card warning">
                        <div class="problem-header">
                          <span class="problem-icon">🌐</span>
                          <h5>HTTP Client迁移导致的性能问题</h5>
                        </div>
                        <div class="problem-content">
                          <p>
                            <strong>场景：</strong>从Apache HttpClient或OkHttp迁移到Java 11的HTTP
                            Client时，出现连接池配置不当、SSL握手失败、或者在高并发场景下性能不如预期的问题。
                          </p>
                          <p>
                            <strong>表现：</strong
                            >接口响应时间增加50%以上，SSL证书验证失败，或者在微服务间调用时出现连接超时。
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="root-cause-analysis">
                    <h4>🔍 问题根源分析</h4>
                    <div class="analysis-content">
                      <div class="cause-item">
                        <span class="cause-icon">🔒</span>
                        <div>
                          <h5>不可变性设计的理解偏差</h5>
                          <p>
                            Java
                            11的集合工厂方法返回的是真正的不可变集合，而不是像Collections.unmodifiableList()那样的视图。开发者往往按照旧的思维模式使用，没有意识到这种根本性的变化。
                          </p>
                        </div>
                      </div>
                      <div class="cause-item">
                        <span class="cause-icon">🔧</span>
                        <div>
                          <h5>HTTP Client配置复杂性</h5>
                          <p>
                            Java 11的HTTP
                            Client虽然功能强大，但其配置方式与传统的HTTP库差异较大，特别是在连接池、超时设置、SSL配置等方面需要重新学习。
                          </p>
                        </div>
                      </div>
                      <div class="cause-item">
                        <span class="cause-icon">📚</span>
                        <div>
                          <h5>迁移文档和最佳实践缺失</h5>
                          <p>
                            官方文档主要关注API使用，但缺乏从现有HTTP库迁移的详细指南，特别是在企业级应用中的性能调优和故障排查方面。
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="solutions-section">
                    <h4>💡 业界主流解决方案与权衡</h4>
                    <div class="solutions-grid">
                      <div class="solution-card recommended">
                        <div class="solution-header">
                          <span class="solution-icon">🛠️</span>
                          <h5>渐进式迁移与封装策略</h5>
                          <span class="solution-badge best">推荐方案</span>
                        </div>
                        <div class="solution-content">
                          <p>
                            <strong>方案：</strong>创建统一的HTTP客户端封装层，内部可以在Java HTTP
                            Client和传统库之间切换，同时建立集合使用的编码规范，明确区分可变和不可变集合的使用场景。
                          </p>
                          <div class="pros-cons">
                            <div class="pros">
                              <h6>✅ 优势</h6>
                              <ul>
                                <li>降低迁移风险，可以逐步切换</li>
                                <li>统一的API接口，便于维护</li>
                                <li>可以根据场景选择最优实现</li>
                                <li>便于性能监控和问题排查</li>
                              </ul>
                            </div>
                            <div class="cons">
                              <h6>❌ 劣势</h6>
                              <ul>
                                <li>增加了代码复杂度</li>
                                <li>需要额外的封装层维护</li>
                                <li>可能无法充分利用新特性</li>
                                <li>团队学习成本增加</li>
                              </ul>
                            </div>
                          </div>
                          <div class="trade-offs">
                            <p>
                              <strong>权衡：</strong
                              >适合大型企业级项目，能够在稳定性和新特性采用之间取得平衡，是目前最稳妥的迁移策略。
                            </p>
                          </div>
                        </div>
                      </div>

                      <div class="solution-card alternative">
                        <div class="solution-header">
                          <span class="solution-icon">🔍</span>
                          <h5>静态分析工具检查</h5>
                          <span class="solution-badge stable">技术方案</span>
                        </div>
                        <div class="solution-content">
                          <p>
                            <strong>方案：</strong
                            >配置SpotBugs、ErrorProne等静态分析工具，自动检测对不可变集合的修改操作，在编译时就发现潜在问题。
                          </p>
                          <div class="pros-cons">
                            <div class="pros">
                              <h6>✅ 优势</h6>
                              <ul>
                                <li>编译时发现问题，避免运行时异常</li>
                                <li>自动化检查，减少人工审查</li>
                                <li>可以集成到CI/CD流程</li>
                                <li>提供详细的错误报告</li>
                              </ul>
                            </div>
                            <div class="cons">
                              <h6>❌ 劣势</h6>
                              <ul>
                                <li>工具配置和规则维护复杂</li>
                                <li>可能产生误报</li>
                                <li>无法检测所有动态场景</li>
                                <li>增加构建时间</li>
                              </ul>
                            </div>
                          </div>
                          <div class="trade-offs">
                            <p>
                              <strong>权衡：</strong
                              >适合有完善DevOps流程的团队，能够有效预防常见错误，但需要投入时间进行工具配置和规则优化。
                            </p>
                          </div>
                        </div>
                      </div>

                      <div class="solution-card temporary">
                        <div class="solution-header">
                          <span class="solution-icon">⏸️</span>
                          <h5>延迟采用新特性</h5>
                          <span class="solution-badge conservative">保守方案</span>
                        </div>
                        <div class="solution-content">
                          <p>
                            <strong>方案：</strong
                            >继续使用传统的集合创建方式和HTTP库，等待社区最佳实践成熟后再考虑迁移。
                          </p>
                          <div class="pros-cons">
                            <div class="pros">
                              <h6>✅ 优势</h6>
                              <ul>
                                <li>避免所有新特性相关风险</li>
                                <li>团队无需额外学习</li>
                                <li>现有代码无需修改</li>
                                <li>工具链兼容性最好</li>
                              </ul>
                            </div>
                            <div class="cons">
                              <h6>❌ 劣势</h6>
                              <ul>
                                <li>错过性能和便利性改进</li>
                                <li>技术债务持续积累</li>
                                <li>与现代Java实践脱节</li>
                                <li>可能影响团队技术形象</li>
                              </ul>
                            </div>
                          </div>
                          <div class="trade-offs">
                            <p>
                              <strong>权衡：</strong
                              >适合风险承受能力极低的关键业务系统，但长期来看会影响技术竞争力和团队发展。
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- 章节总结与思维导图 -->
            <section id="topic-5" class="topic-section chapter-summary" ref="topic5">
              <ExpandableSection
                title="📊 章节总结与知识体系图"
                :concept-data="chapterSummaryData"
                @interaction="handleInteraction"
              >
                <div class="summary-content">
                  <h3>🎯 本章核心收获</h3>
                  <div class="key-takeaways">
                    <div class="takeaway-item">
                      <span class="takeaway-icon">🔄</span>
                      <div>
                        <h4>理解Java双重身份</h4>
                        <p>区分Java语言和Java平台，掌握编译运行机制</p>
                      </div>
                    </div>
                    <div class="takeaway-item">
                      <span class="takeaway-icon">📅</span>
                      <div>
                        <h4>新发布模型</h4>
                        <p>6个月发布周期，LTS版本策略，预览特性机制</p>
                      </div>
                    </div>
                    <div class="takeaway-item">
                      <span class="takeaway-icon">🔍</span>
                      <div>
                        <h4>var类型推断</h4>
                        <p>局部变量类型推断，提升代码可读性和开发效率</p>
                      </div>
                    </div>
                    <div class="takeaway-item">
                      <span class="takeaway-icon">⚡</span>
                      <div>
                        <h4>Java 11特性</h4>
                        <p>HTTP客户端、字符串增强、文件操作简化等实用改进</p>
                      </div>
                    </div>
                  </div>

                  <div class="mindmap-container">
                    <h3>🧠 现代Java知识体系图</h3>
                    <div class="mindmap-wrapper">
                      <div id="chapter1-mindmap" class="mermaid-container"></div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>
          </main>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import ExpandableSection from '@/components/ExpandableSection.vue'
import JavaCompilationAnimation from '@/components/JavaCompilationAnimation.vue'
import CodePlayground from '@/components/CodePlayground.vue'

// 响应式数据
const progress = ref(0)
const currentTopic = ref(0)
const activeTab = ref(0)
const showNotes = ref(false)
const showQuizModal = ref(false)

// 课程主题
const courseTopics = [
  {
    title: 'Java 语言 vs. Java 平台',
    description: '理解语言与平台的根本区别，掌握跨平台原理',
  },
  {
    title: '新的 Java 发布模型',
    description: '了解时间驱动的发布周期和LTS版本策略',
  },
  {
    title: '增强的类型推断 (var)',
    description: '掌握var关键字的使用和局限性',
  },
  {
    title: '语言与平台演进机制',
    description: '学习语法糖、JEPs、孵化和预览特性',
  },
  {
    title: 'Java 11 重要变更',
    description: '探索集合工厂、HTTP/2客户端等新特性',
  },
  {
    title: '章节总结',
    description: '知识体系图和核心收获总结',
  },
]

// 解释标签页
const explanationTabs = [
  { title: '概念定义', type: 'definitions', content: [] },
  { title: '发布历史', type: 'timeline', content: [] },
]

// 数据定义
const javaLanguagePlatformData = {
  keyPoints: [
    '语言：静态类型、面向对象的编程语言（.java文件）',
    '平台：提供运行环境的软件集合，核心是JVM（.class文件）',
    '契约：.class文件格式是语言和平台间的唯一桥梁',
    '执行：编译+解释+JIT的动态编译系统',
  ],
}

const releaseModelData = {
  keyPoints: [
    '特性发布：每6个月发布一个新版本',
    'LTS版本：每2-3年指定一个长期支持版本',
    '时间驱动：不再等待重大特性，按时间表发布',
    '主线开发：特性分支开发，主干定期发布',
  ],
}

const varKeywordData = {
  keyPoints: [
    '局部变量类型推断：编译器自动推断变量类型',
    '静态类型：var不是动态类型，编译时确定类型',
    '减少冗余：简化复杂泛型类型的声明',
    '局限性：仅限局部变量，需要初始化',
  ],
}

const evolutionMechanismData = {
  keyPoints: [
    '语法糖：编译器层面的简化，成本最低',
    'JEPs：轻量级增强提案，快速推动变更',
    'JSRs：重量级规范请求，适合成熟技术',
    '预览特性：收集反馈，验证设计',
    '孵化特性：新API的试验场',
  ],
}

const java11ChangesData = {
  keyPoints: [
    '集合工厂：简化不可变集合创建',
    'HTTP/2客户端：现代化网络编程',
    '单文件程序：简化脚本开发',
    '移除EE模块：精简核心JDK',
    'String新方法：增强字符串处理',
  ],
}

const chapterSummaryData = {
  keyPoints: [
    '掌握Java语言与平台的本质区别',
    '理解现代Java的发布模型和版本策略',
    '学会使用var关键字进行类型推断',
    '了解Java生态的演进机制和特性管理',
  ],
}

const javaReleases = [
  { version: '8', date: '2014年3月', features: 'Lambda表达式、Stream API', isLTS: true },
  { version: '11', date: '2018年9月', features: 'HTTP/2客户端、单文件程序', isLTS: true },
  { version: '17', date: '2021年9月', features: 'Sealed Classes正式版', isLTS: true },
]

const varExamples = [
  {
    title: '基本用法对比',
    code: `var message = "Hello, Java!";
System.out.println("消息: " + message);`,
    explanation: 'var关键字简化了变量声明',
  },
]

const collectionFactoryExamples = [
  {
    title: '创建不可变List',
    code: `List<String> list = List.of("apple", "banana", "cherry");
System.out.println("列表: " + list);`,
    explanation: 'List.of()创建不可变集合',
  },
]

// 方法
const scrollToTopic = (index: number) => {
  currentTopic.value = index

  // 滚动到对应的主题部分
  const targetElement = document.getElementById(`topic-${index}`)
  if (targetElement) {
    targetElement.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
    })
  }
}

const toggleNotes = () => {
  showNotes.value = !showNotes.value
}

const showQuiz = () => {
  showQuizModal.value = true
}

const handleInteraction = (type: string) => {
  console.log('Interaction:', type)
}

const handleCodeRun = (data: any) => {
  console.log('Code run:', data)
}

// 滚动监听函数
const handleScroll = () => {
  const sections = courseTopics
    .map((_, index) => document.getElementById(`topic-${index}`))
    .filter(Boolean)

  if (sections.length === 0) return

  // 获取当前滚动位置
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop
  const windowHeight = window.innerHeight

  // 找到当前可视区域内的章节
  let activeIndex = 0
  for (let i = 0; i < sections.length; i++) {
    const section = sections[i]
    if (section) {
      const rect = section.getBoundingClientRect()
      const sectionTop = rect.top + scrollTop

      // 如果章节顶部在视窗上半部分，则认为是当前活跃章节
      if (sectionTop <= scrollTop + windowHeight * 0.3) {
        activeIndex = i
      } else {
        break
      }
    }
  }

  currentTopic.value = activeIndex
}

// 初始化Mermaid
onMounted(async () => {
  // 添加滚动监听
  window.addEventListener('scroll', handleScroll, { passive: true })

  // 初始化当前章节
  handleScroll()

  try {
    console.log('开始初始化 Mermaid...')
    const mermaid = await import('mermaid')
    console.log('Mermaid 模块加载成功:', mermaid)

    mermaid.default.initialize({
      startOnLoad: false,
      theme: 'default',
      securityLevel: 'loose',
    })
    console.log('Mermaid 初始化完成')

    // 延迟渲染以确保DOM已加载
    setTimeout(async () => {
      try {
        console.log('开始渲染 Mermaid 图表...')

        // 创建思维导图内容
        const mindmapContent = `mindmap
  root((现代Java))
    Java双重身份
      Java语言
      Java平台
      编译运行
    新发布模型
      时间驱动
      LTS版本
      特性管理
    var类型推断
      局部变量
      使用场景
      最佳实践
    演进机制
      JEP流程
      特性分类
      向后兼容
    Java11亮点
      HTTP客户端
      字符串增强
      文件操作`

        const container = document.getElementById('chapter1-mindmap')
        if (container) {
          console.log('找到容器，开始渲染...')
          const { svg } = await mermaid.default.render('chapter1-mindmap-svg', mindmapContent)
          container.innerHTML = svg
          console.log('Mermaid 图表渲染完成')
        } else {
          console.error('未找到思维导图容器')
        }
      } catch (renderError) {
        console.error('Mermaid 渲染错误:', renderError)
      }
    }, 1000) // 增加延迟时间
  } catch (error) {
    console.error('Mermaid 初始化失败:', error)
  }
})

onUnmounted(() => {
  // 移除滚动监听
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped>
/* 基础样式 */
.java-chapter1 {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.chapter-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 3rem 0;
  text-align: center;
}

.chapter-title {
  font-size: 2.5rem;
  margin: 0 0 1rem 0;
  font-weight: 700;
}

.chapter-subtitle {
  font-size: 1.2rem;
  margin: 0 0 2rem 0;
  opacity: 0.9;
}

.progress-bar {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 25px;
  height: 8px;
  position: relative;
  max-width: 400px;
  margin: 0 auto;
}

.progress-fill {
  background: white;
  height: 100%;
  border-radius: 25px;
  transition: width 0.3s ease;
}

.progress-text {
  position: absolute;
  top: -30px;
  right: 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.content-layout {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 3rem;
  margin-top: 3rem;
}

.sidebar {
  position: sticky;
  top: 2rem;
  height: fit-content;
}

.outline {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.outline h3 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.2rem;
}

.outline-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 0.5rem;
}

.outline-item:hover {
  background: #f8f9fa;
}

.outline-item.active {
  background: #e3f2fd;
  border-left: 4px solid #2196f3;
}

.outline-number {
  width: 30px;
  height: 30px;
  background: #667eea;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  flex-shrink: 0;
}

.outline-content h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1rem;
}

.outline-content p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

.toolbar {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.tool-button {
  background: white;
  border: 2px solid #e9ecef;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.tool-button:hover {
  background: #f8f9fa;
  border-color: #667eea;
}

.main-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.topic-section {
  margin-bottom: 2rem;
}

.topic-section:last-child {
  margin-bottom: 0;
}

/* 深度解读样式 */
.deep-dive {
  margin-top: 2rem;
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.explanation-tabs {
  margin-top: 1.5rem;
}

.tab-buttons {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.tab-button {
  background: white;
  border: 2px solid #e9ecef;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.tab-button.active {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.tab-panel h4 {
  margin: 0 0 1.5rem 0;
  color: #333;
}

/* 发布时间线样式 */
.release-timeline {
  padding: 2rem;
}

.timeline-container {
  position: relative;
  padding-left: 2rem;
}

.timeline-container::before {
  content: '';
  position: absolute;
  left: 1rem;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #e9ecef;
}

.timeline-item {
  position: relative;
  margin-bottom: 2rem;
  padding-left: 2rem;
}

.timeline-marker {
  position: absolute;
  left: -2rem;
  top: 0.5rem;
  width: 12px;
  height: 12px;
  background: #667eea;
  border-radius: 50%;
  border: 3px solid white;
  box-shadow: 0 0 0 2px #e9ecef;
}

.timeline-item.lts .timeline-marker {
  background: #4caf50;
  width: 16px;
  height: 16px;
  left: -2.2rem;
  top: 0.3rem;
}

.timeline-content h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
}

.timeline-date {
  color: #666;
  font-size: 0.9rem;
  margin: 0 0 0.5rem 0;
}

.timeline-features {
  color: #555;
  margin: 0;
}

.lts-badge {
  background: #4caf50;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
}

/* 演进机制样式 */
.evolution-mechanisms {
  padding: 2rem;
}

.mechanisms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.mechanism-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border-left: 4px solid;
}

.mechanism-card.syntactic-sugar {
  border-left-color: #4caf50;
}

.mechanism-card.jeps-jsrs {
  border-left-color: #2196f3;
}

.mechanism-card.preview-features {
  border-left-color: #ff9800;
}

.mechanism-card.incubating-features {
  border-left-color: #9c27b0;
}

.mechanism-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.mechanism-icon {
  font-size: 1.5rem;
}

.mechanism-header h4 {
  margin: 0;
  flex: 1;
  color: #333;
}

.cost-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
}

.cost-badge.low {
  background: #e8f5e8;
  color: #4caf50;
}

.cost-badge.medium {
  background: #e3f2fd;
  color: #2196f3;
}

.cost-badge.high {
  background: #fff3e0;
  color: #ff9800;
}

.mechanism-content p {
  margin: 0.5rem 0;
  color: #666;
  line-height: 1.6;
}

/* Java 11 特性样式 */
.java11-features {
  padding: 2rem;
}

.feature-detail {
  margin-bottom: 3rem;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.feature-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.feature-icon {
  font-size: 2rem;
}

.feature-header h4 {
  margin: 0;
  flex: 1;
  font-size: 1.3rem;
}

.jep-badge {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
}

.feature-content {
  padding: 2rem;
}

.feature-description p {
  color: #666;
  line-height: 1.6;
  font-size: 1.1rem;
  margin: 0 0 2rem 0;
}

@media (max-width: 768px) {
  .content-layout {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .sidebar {
    position: static;
    order: -1;
  }

  .chapter-title {
    font-size: 2rem;
  }

  .mechanisms-grid {
    grid-template-columns: 1fr;
  }
}

/* 章节总结样式 */
.chapter-summary {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  margin: 2rem 0;
}

.summary-content {
  padding: 2rem;
}

.key-takeaways {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.takeaway-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.takeaway-item:hover {
  transform: translateY(-2px);
}

.takeaway-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.takeaway-item h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1.1rem;
}

.takeaway-item p {
  margin: 0;
  color: #666;
  line-height: 1.5;
}

/* 思维导图样式 */
.mindmap-container {
  margin-top: 3rem;
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.mindmap-container h3 {
  text-align: center;
  color: #333;
  margin-bottom: 2rem;
}

.mindmap-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 600px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.mermaid-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 不隐藏pre标签，让Mermaid能够读取内容 */
.mermaid-diagram pre.mermaid {
  visibility: hidden; /* 使用visibility而不是display，保持元素存在但不可见 */
  position: absolute;
  top: -9999px;
}

/* Mermaid图表样式覆盖 */
.mindmap-wrapper .mermaid {
  max-width: 100%;
  height: auto;
}

.mindmap-wrapper svg {
  max-width: 100%;
  height: auto;
}

@media (max-width: 768px) {
  .key-takeaways {
    grid-template-columns: 1fr;
  }

  .mindmap-wrapper {
    min-height: 400px;
  }

  .takeaway-item {
    flex-direction: column;
    text-align: center;
  }

  .takeaway-icon {
    align-self: center;
  }

  /* 项目实践踩坑响应式 */
  .problem-cards {
    grid-template-columns: 1fr;
  }

  .solutions-grid {
    grid-template-columns: 1fr;
  }

  .pros-cons {
    grid-template-columns: 1fr;
  }

  .solution-header {
    flex-direction: column;
    align-items: flex-start;
  }
}

/* 项目实践踩坑与解决方案样式 */
.real-world-problems {
  margin-top: 3rem;
  padding: 2rem;
  background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
  border-radius: 12px;
  border-left: 5px solid #ff9800;
}

.real-world-problems h3 {
  margin: 0 0 2rem 0;
  color: #e65100;
  font-size: 1.5rem;
  font-weight: 700;
}

.problem-section,
.root-cause-analysis,
.solutions-section {
  margin-bottom: 2.5rem;
}

.problem-section h4,
.root-cause-analysis h4,
.solutions-section h4 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
}

.problem-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.problem-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #f44336;
}

.problem-card.warning {
  border-left-color: #ff9800;
}

.problem-card.critical {
  border-left-color: #f44336;
}

.problem-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.problem-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.problem-header h5 {
  margin: 0;
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
}

.problem-content p {
  margin: 0.75rem 0;
  line-height: 1.6;
  color: #555;
}

.problem-content code {
  background: #f5f5f5;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
}

.analysis-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.cause-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.cause-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
  margin-top: 0.25rem;
}

.cause-item h5 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
}

.cause-item p {
  margin: 0;
  line-height: 1.6;
  color: #555;
}

.solutions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.solution-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border-top: 4px solid #4caf50;
}

.solution-card.alternative {
  border-top-color: #2196f3;
}

.solution-card.temporary {
  border-top-color: #ff9800;
}

.solution-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.solution-header h5 {
  margin: 0;
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.solution-icon {
  font-size: 1.5rem;
}

.solution-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.solution-badge.best {
  background: #e8f5e8;
  color: #2e7d32;
}

.solution-badge.stable {
  background: #e3f2fd;
  color: #1565c0;
}

.solution-badge.quick {
  background: #fff3e0;
  color: #ef6c00;
}

.solution-badge.risky {
  background: #ffebee;
  color: #c62828;
}

.solution-badge.conservative {
  background: #f3e5f5;
  color: #7b1fa2;
}

.solution-content p {
  margin: 0 0 1.5rem 0;
  line-height: 1.6;
  color: #555;
}

.pros-cons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.pros,
.cons {
  padding: 1rem;
  border-radius: 8px;
}

.pros {
  background: #e8f5e8;
  border-left: 3px solid #4caf50;
}

.cons {
  background: #ffebee;
  border-left: 3px solid #f44336;
}

.pros h6,
.cons h6 {
  margin: 0 0 0.75rem 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.pros ul,
.cons ul {
  margin: 0;
  padding-left: 1.2rem;
  list-style-type: disc;
}

.pros li,
.cons li {
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  line-height: 1.4;
}

.trade-offs {
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 3px solid #6c757d;
}

.trade-offs p {
  margin: 0;
  font-size: 0.95rem;
  line-height: 1.5;
  color: #495057;
}
</style>

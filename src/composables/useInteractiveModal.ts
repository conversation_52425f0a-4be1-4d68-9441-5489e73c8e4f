import { ref } from 'vue'

interface ModalData {
  title: string
  type: string
  data?: any
}

export function useInteractiveModal() {
  const isModalVisible = ref(false)
  const modalData = ref<ModalData>({
    title: '',
    type: '',
    data: null
  })

  const openModal = (type: string, customTitle?: string, data?: any) => {
    const titleMap: Record<string, string> = {
      // 通用交互
      'code-comparison': '📊 代码对比',
      'concept-demo': '🎯 概念演示',
      'interactive-playground': '💻 交互式演练场',
      
      // 第6章 - 并发编程
      'lock-demo': '🔒 Lock/Condition 演示',
      'atomic-demo': '⚛️ 原子类演示',
      'blocking-queue-demo': '📦 阻塞队列演示',
      'future-demo': '🔮 Future/CompletableFuture 演示',
      'executor-demo': '🏭 Executor框架演示',
      'deadlock-prevention': '🚫 死锁预防演示',
      'lock-ordering': '📋 锁排序演示',
      'trylock-demo': '⏰ tryLock演示',
      'copyonwrite-demo': '📝 CopyOnWriteArrayList演示',
      'queue-comparison': '⚖️ 队列类型对比',
      'async-composition': '🔗 异步组合演示',
      'thread-pool-tuning': '⚙️ 线程池调优',
      
      // 第7章 - 性能优化
      'memory-hierarchy-demo': '🏗️ 内存层级演示',
      'cache-miss-simulation': '💥 缓存未命中模拟',
      'false-sharing-demo': '🔄 伪共享演示',
      'gc-process-demo': '🗑️ GC过程演示',
      'generational-demo': '👶 分代回收演示',
      'gc-tuning-simulator': '⚙️ GC调优模拟器',
      'jit-compilation-demo': '⚡ JIT编译演示',
      'method-inlining-demo': '📎 方法内联演示',
      'jfr-recording-demo': '📹 JFR记录演示',
      'jmc-analysis-tour': '🔍 JMC分析导览',
      'production-monitoring': '📊 生产监控实践',
      
      // 第8章 - JVM语言
      'language-comparison': '🔤 语言特性对比',
      'pyramid-demo': '🏔️ 多语言金字塔演示',
      'selection-checklist': '📋 技术选型清单',
      'risk-assessment': '⚠️ 风险评估器',
      'bytecode-demo': '🔍 字节码对比',
      'interop-demo': '🔗 互操作演示',
      
      // 第9章 - Kotlin
      'java-kotlin-converter': '🔄 Java/Kotlin 代码转换器',
      'syntax-comparison': '📊 语法对比工具',
      'code-golf': '⛳ 代码简化挑战',
      'data-class-generator': '🏗️ Data Class 生成器',
      'inheritance-demo': '🔒 继承安全演示',
      'null-safety-playground': '🛡️ 空安全演练场',
      'smart-casting-demo': '🧠 智能转换演示',
      'coroutines-playground': '🚀 协程演练场',
      'concurrency-comparison': '⚡ 并发模型对比',
      'annotation-generator': '🏷️ 注解生成器',
    }

    modalData.value = {
      title: customTitle || titleMap[type] || '🎮 交互演示',
      type,
      data
    }
    isModalVisible.value = true
  }

  const closeModal = () => {
    isModalVisible.value = false
    modalData.value = {
      title: '',
      type: '',
      data: null
    }
  }

  const handleInteraction = (type: string, customTitle?: string, data?: any) => {
    console.log('交互类型:', type)
    openModal(type, customTitle, data)
  }

  return {
    isModalVisible,
    modalData,
    openModal,
    closeModal,
    handleInteraction
  }
}
